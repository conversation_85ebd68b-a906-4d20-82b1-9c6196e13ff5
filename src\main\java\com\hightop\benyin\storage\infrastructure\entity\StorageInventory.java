package com.hightop.benyin.storage.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.product.domain.dto.ProductTreeDto;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓储管理-库品管理实体
 * <AUTHOR>
 * @date 2023-11-03 11:28:56
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_storage_inventory")
@ApiModel
public class StorageInventory {

    /**
     * 所属单元字典码
     */
    public static final String UNIT = "3200";

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("id")
    Long id;

    @TableField("code")
    @ApiModelProperty("库品编号")
    @Excel(name = "物品编号", orderNum = "1",width = 20)
    String code;

    @TableField("name")
    @ApiModelProperty("库品名称")
    @Excel(name = "物品名称", orderNum = "2",width = 30)
    String name;

    @TableField("location")
    @ApiModelProperty("储位")
    @Excel(name = "储位", orderNum = "14",width = 20)
    String location;

    @TableField("warehouse_id")
    @ApiModelProperty("仓库id")
    Long warehouseId;

    @TableField("article_type")
    @ApiModelProperty("物品类型1组合0单品")
    Integer articleType;

    @TableField(exist = false)
    @ApiModelProperty("仓库类型")
    String warehouseType;

    @TableField(exist = false)
    @ApiModelProperty("仓库名称")
    @Excel(name = "归属仓库", orderNum = "6",width = 20)
    String warehouseName;

    @TableField("sum_warehouse_number")
    @ApiModelProperty("库存量")
    @Excel(name = "库存量", orderNum = "8")
    Integer sumWarehouseNumber;

    @TableField("out_warehouse_number")
    @ApiModelProperty("出库量")
    @Excel(name = "出库量", orderNum = "9")
    Integer outWarehouseNumber;

    @TableField("run_warehouse_number")
    @ApiModelProperty("在途量")
    @Excel(name = "在途量", orderNum = "9")
    Integer runWarehouseNumber;

    @TableField("alarm_number")
    @ApiModelProperty("库存预警值")
    @Excel(name = "预警值", orderNum = "13")
    Integer alarmNumber;

    @TableField("safe_number")
    @ApiModelProperty("安全库存")
    Integer safeNumber;


    @TableField("average_price")
    @ApiModelProperty("均价")
    @JsonAmount
    Long averagePrice;

    @TableField("is_unsalable")
    @ApiModelProperty("呆滞状态")
    Boolean isUnsalable;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("入库记录(流水记录)")
    List<StorageInventoryBatch> inWarehouseList = new ArrayList<>();

    @TableField(exist = false)
    @ApiModelProperty("出库记录(流水记录)")
    List<StorageWarehouseFlow> outWarehouseList = new ArrayList<>();

    @TableField(exist = false)
    @ApiModelProperty("物品基本信息")
    StorageArticle article = new StorageArticle();

    @ApiModelProperty("供应商id(制造商)")
    @TableField(exist = false)
    Manufacturer manufacturer;

    @ApiModelProperty("适用机型")
    @TableField(exist = false)
    List<ProductTreeDto> productTreeDtoList = new ArrayList<>();

    @ApiModelProperty("销售状态")
    @TableField(exist = false)
    @Excel(name = "销售状态", orderNum = "7",replace = {"未售_0","在售_1"})
    Integer saleStatus;

    @ApiModelProperty("oem")
    @TableField(exist = false)
    @Excel(name = "OEM编号", orderNum = "4")
    String numberOem;

    @TableField(exist = false)
    @ApiModelProperty("零件品牌")
    @Excel(name = "品牌", orderNum = "3")
    String partBrand;

    @ApiModelProperty("BomID 编号")
    @TableField(exist = false)
    Long bomId;

    @ApiModelProperty("所属单元(字典项码)")
    @DictItemBind(UNIT)
    @TableField(exist = false)
    DictItemEntry bomUnit;

    @ApiModelProperty("机型 编号")
    @TableField(exist = false)
    Long productId;

    @ApiModelProperty("工程师数量")
    @TableField(exist = false)
    @Excel(name = "工程师数量", orderNum = "10")
    Integer engineerNum;

    @ApiModelProperty("领料单数量")
    @TableField(exist = false)
    @Excel(name = "客户仓数量", orderNum = "11")
    Integer applyNum;

    @ApiModelProperty("总数量")
    @TableField(exist = false)
    @Excel(name = "总数量", orderNum = "12")
    Integer totalNum;

    @ApiModelProperty("机型")
    @TableField(exist = false)
    ProductTree productTree;

    @ApiModelProperty("物品图片")
    @TableField(exist = false)
    CosObjectList imageFiles;

    @TableField(exist = false)
    @ApiModelProperty("制造商渠道(字典项码)")
    @DictItemBind(StorageArticle.CHANNEL)
    @Excel(name = "制造商渠道", orderNum = "5",enumExportField = "label",width = 20)
    DictItemEntry manufacturerChannel;

    @TableField(exist = false)
    @ApiModelProperty("按批次计算的库存金额")
    @JsonAmount
    Long batchInventoryAmount;
}
