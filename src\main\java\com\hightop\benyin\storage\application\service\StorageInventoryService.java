package com.hightop.benyin.storage.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.benyin.item.infrastructure.entity.SaleSku;
import com.hightop.benyin.product.domain.dto.ProductTreeDto;
import com.hightop.benyin.product.domain.service.PartProductTreeDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductDevice;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.purchase.api.dto.PurchaseRequireModifyDto;
import com.hightop.benyin.purchase.api.dto.query.PurchaseRequireQuery;
import com.hightop.benyin.purchase.application.service.PurchaseRequireService;
import com.hightop.benyin.purchase.infrastructure.entity.PurchaseRequire;
import com.hightop.benyin.purchase.infrastructure.enums.PurchaseRequireSourceEnum;
import com.hightop.benyin.purchase.infrastructure.enums.PurchaseRequireStatusEnum;
import com.hightop.benyin.storage.api.dto.StorageInventoryAddArticleDto;
import com.hightop.benyin.storage.api.dto.StorageInventoryAddDto;
import com.hightop.benyin.storage.api.dto.excel.StorageInventoryExcel;
import com.hightop.benyin.storage.api.dto.query.InventoryUnsalableQuery;
import com.hightop.benyin.storage.api.dto.query.MechineWarehouseQuery;
import com.hightop.benyin.storage.api.dto.query.StorageInventoryQuery;
import com.hightop.benyin.storage.application.handler.InventoryExcelVerifyHandler;
import com.hightop.benyin.storage.application.vo.InventoryUnsalableVo;
import com.hightop.benyin.storage.application.vo.MechineWarehouseVo;
import com.hightop.benyin.storage.application.vo.StorageInventoryTotalVo;
import com.hightop.benyin.storage.domain.dto.UnsalableModifyDto;
import com.hightop.benyin.storage.domain.service.*;
import com.hightop.benyin.storage.infrastructure.entity.*;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仓储管理-库品管理服务
 *
 * <AUTHOR>
 * @date 2023-11-03 11:28:56
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class StorageInventoryService {
    StorageInventoryServiceDomain storageInventoryServiceDomain;

    /**
     * 流水记录
     */
    StorageWarehouseFlowServiceDomain storageWarehouseFlowServiceDomain;
    /**
     * 仓库管理
     */
    WarehouseServiceDomain warehouseServiceDomain;
    /**
     * 物品管理
     */
    StorageArticleServiceDomain storageArticleServiceDomain;
    /**
     * 制造商服务
     */
    ManufacturerServiceDomain manufacturerServiceDomain;
    /**
     * 零件、机型关系服务
     */
    PartProductTreeDomainService partProductTreeDomainService;

    StorageInWarehouseGoodsServiceDomain storageInWarehouseGoodsServiceDomain;
    StorageInWarehouseServiceDomain storageInWarehouseServiceDomain;
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;
    ProductTreeDomainService productTreeDomainService;
    PurchaseRequireService purchaseRequireService;

    /**
     * 仓储管理-库品管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<StorageInventory> page(StorageInventoryQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                        this.storageInventoryServiceDomain.getInventoryList(pageQuery))
                .peek(p -> {

                    // 物品信息回显
                    StorageArticle article = this.storageArticleServiceDomain.getByCode(p.getCode());
                    Optional.ofNullable(article).ifPresent(i -> p.setArticle(article));

                    Long productId = p.getProductId();
                    if (productId != null) {
                        ProductTree productTree = productTreeDomainService.getById(productId);
                        if (productTree != null) {
                            p.setProductTree(productTree);
                        }
                    }
                });
    }


    public StorageInventoryTotalVo getInventoryTotal(StorageInventoryQuery pageQuery) {
        StorageInventoryTotalVo storageInventoryTotalVo = new StorageInventoryTotalVo();
        List<StorageInventory> storageInventories = this.storageInventoryServiceDomain.getInventoryList(pageQuery);
        storageInventoryTotalVo.setTotalNum(storageInventories.stream().mapToInt(StorageInventory::getTotalNum).sum());
        storageInventoryTotalVo.setInventoryNum(storageInventories.stream().mapToInt(StorageInventory::getSumWarehouseNumber).sum());
        storageInventoryTotalVo.setApplyNum(storageInventories.stream().mapToInt(StorageInventory::getApplyNum).sum());
        storageInventoryTotalVo.setEngineerNum(storageInventories.stream().mapToInt(StorageInventory::getEngineerNum).sum());
        storageInventoryTotalVo.setAmount(storageInventories.stream().mapToLong(v->{
            // 优先使用按批次计算的金额，如果为空则使用均价计算
            if(v.getBatchInventoryAmount() != null){
                return v.getBatchInventoryAmount();
            } else {
                if(v.getAveragePrice() == null){
                    v.setAveragePrice(0L);
                }
                return v.getAveragePrice() * v.getTotalNum();
            }
        }).sum());
        return storageInventoryTotalVo;
    }
    public Boolean downloadInventory(HttpServletResponse response, StorageInventoryQuery pageQuery) {
        try {
            //查询数据
            List<StorageInventory> excelList = this.storageInventoryServiceDomain.getInventoryList(pageQuery);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "耗材仓库明细.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), StorageInventory.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public DataGrid<MechineWarehouseVo> mechinePage(MechineWarehouseQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                storageInventoryBatchServiceDomain.selectJoinList(MechineWarehouseVo.class
                        , MPJWrappers.lambdaJoin().distinct()
                                .selectAll(StorageInventoryBatch.class)
                                .selectAs(Warehouse::getName, MechineWarehouseVo::getWarehouseName)
                                .selectAs(StorageArticle::getPartBrand, MechineWarehouseVo::getBrand)
                                .selectAs(ProductTree::getName, MechineWarehouseVo::getMachine)
                                .selectAs(ProductDevice::getType, MechineWarehouseVo::getType)
                                .selectAs(ProductDevice::getColor, MechineWarehouseVo::getColor)
                                .selectAs(ProductDevice::getHostType, MechineWarehouseVo::getHostType)
                                .selectAs(ProductDevice::getProduce, MechineWarehouseVo::getProduce)
                                .selectAs(Item::getSaleStatus, MechineWarehouseVo::getSaleStatus)
                                .selectAs(StorageArticle::getId, MechineWarehouseVo::getArticleId)
                                .leftJoin(StorageArticle.class, StorageArticle::getCode, StorageInventoryBatch::getCode)
                                .leftJoin(Warehouse.class, Warehouse::getId, StorageInventoryBatch::getWarehouseId)
                                .leftJoin(ProductDevice.class, ProductDevice::getProductId, StorageArticle::getProductId)
                                .leftJoin(ProductTree.class, ProductTree::getId, StorageArticle::getProductId)
                                .leftJoin(StorageInventory.class, StorageInventory::getCode, StorageInventoryBatch::getCode)
                                .leftJoin(SaleSku.class, SaleSku::getInvSkuId, StorageInventory::getId)
                                .leftJoin(Item.class, Item::getId, SaleSku::getItemId)
                                .isNotNull(StorageArticle::getProductId)
                                .gt(StorageInventoryBatch::getRemWarehouseNumber, 0)
                                .eq(StringUtils.isNotBlank(pageQuery.getType()), ProductDevice::getType, pageQuery.getType())
                                .eq(StringUtils.isNotBlank(pageQuery.getHostType()), ProductDevice::getHostType, pageQuery.getHostType())
                                .eq(StringUtils.isNotBlank(pageQuery.getColor()), ProductDevice::getColor, pageQuery.getColor())
                                .like(StringUtils.isNotBlank(pageQuery.getBrand()), StorageArticle::getPartBrand, pageQuery.getBrand())
                                .like(StringUtils.isNotBlank(pageQuery.getMachine()), ProductTree::getName, pageQuery.getMachine())
                                .like(StringUtils.isNotBlank(pageQuery.getCode()), StorageInventoryBatch::getCode, pageQuery.getCode())
                                .like(StringUtils.isNotBlank(pageQuery.getBatchCode()), StorageInventoryBatch::getBatchCode, pageQuery.getBatchCode())
                                .like(StringUtils.isNotBlank(pageQuery.getLocation()), StorageInventoryBatch::getLocation, pageQuery.getLocation())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getProductIds()), StorageArticle::getProductId, pageQuery.getProductIds())
                                .eq(StringUtils.isNotBlank(pageQuery.getSaleStatus()), Item::getSaleStatus, pageQuery.getSaleStatus())
                                .orderByDesc(StorageInventoryBatch::getCreatedAt)
                )
        );
    }

    /**
     * 库存新增
     * 校验：物品在指定仓库已存在则不能入库
     * 填充：库存量都为0
     *
     * @param addDto
     * @return: {@link boolean}
     * @Author: xhg
     * @Date: 2023/12/22 17:21
     */
    public boolean add(StorageInventoryAddDto addDto) {
        if (CollectionUtils.isNotEmpty(addDto.getArticleDtoList())) {
            List<StorageInventory> storageInventories = new ArrayList<>();
            for (StorageInventoryAddArticleDto d : addDto.getArticleDtoList()) {
                StorageInventory storageInventory = this.storageInventoryServiceDomain.lambdaQuery()
                        .eq(StorageInventory::getCode, d.getCode())
                        .eq(StorageInventory::getWarehouseId, addDto.getWarehouseId())
                        .one();
                // 唯一物品
                StorageArticle article = storageArticleServiceDomain.lambdaQuery()
                        .eq(StorageArticle::getCode, d.getCode()).one();
                if (null != storageInventory) {
                    throw new MaginaException(String.format("物品[%s]在该仓库中已有库存,无法继续添加!", article.getName()));
                }
                StorageInventory a = new StorageInventory();
                a.setCode(d.getCode());
                a.setArticleType(article.getArticleType());
                a.setLocation(d.getLocation());
                a.setWarehouseId(addDto.getWarehouseId());
                a.setName(article.getName());
                a.setSumWarehouseNumber(0);
                a.setOutWarehouseNumber(0);
                a.setRunWarehouseNumber(0);
                storageInventories.add(a);
            }
            if (storageInventories.size() > 0) {
                return this.storageInventoryServiceDomain.saveBatch(storageInventories);
            }
        }
        return false;
    }

    /**
     * 仓储管理-库品管理修改
     *
     * @param storageInventory {@link StorageInventory}
     * @return true/false
     */
    public boolean updateById(StorageInventory storageInventory) {
        return this.storageInventoryServiceDomain.updateById(storageInventory);
    }

    /**
     * 机器管理-修改
     *
     * @param storageInventoryBatch {@link StorageInventory}
     * @return true/false
     */
    public boolean updateBatch(StorageInventoryBatch storageInventoryBatch) {
        return this.storageInventoryBatchServiceDomain.updateById(storageInventoryBatch);
    }

    /**
     * 仓储管理-库品管理修改
     *
     * @param unsalableModifyDto {@link StorageInventory}
     * @return true/false
     */
    public boolean updateUnsalable(UnsalableModifyDto unsalableModifyDto) {
        storageInventoryServiceDomain.lambdaUpdate()
                .set(StorageInventory::getIsUnsalable, unsalableModifyDto.getIsUnsalable())
                .eq(StorageInventory::getId, unsalableModifyDto.getId()).update();
        return Boolean.TRUE;
    }

    /**
     * 仓储管理-库品管理删除
     *
     * @param id id
     * @return true/false
     */
    public boolean removeById(Long id) {
        return this.storageInventoryServiceDomain.removeById(id);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    public StorageInventory getOne(Integer id) {
        LambdaQueryWrapper<StorageInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageInventory::getId, id);
        StorageInventory storageInventory = storageInventoryServiceDomain.getOne(wrapper);
        //物品基本信息
        LambdaQueryWrapper<StorageArticle> articleWrapper = new LambdaQueryWrapper<>();
        articleWrapper.eq(StorageArticle::getCode, storageInventory.getCode());
        StorageArticle article = storageArticleServiceDomain.getOne(articleWrapper);
        storageInventory.setArticle(article);

        //适用机型
        if (article.getPartId() != null) {
            List<ProductTreeDto> productTreeDtos = partProductTreeDomainService.getByPartId(article.getPartId());
            storageInventory.setProductTreeDtoList(productTreeDtos);
        }

        //仓库基本信息
        LambdaQueryWrapper<Warehouse> warehouseWrapper = new LambdaQueryWrapper<>();
        warehouseWrapper.eq(Warehouse::getId, storageInventory.getWarehouseId());
        Warehouse warehouse = warehouseServiceDomain.getOne(warehouseWrapper);
        storageInventory.setWarehouseName(warehouse.getName());
        storageInventory.setWarehouseType(warehouse.getType().getLabel());

        //供应商(制造商)
        LambdaQueryWrapper<Manufacturer> manufacturerWrapper = new LambdaQueryWrapper<>();
        manufacturerWrapper.eq(Manufacturer::getId, article.getManufacturerId());
        Manufacturer manufacturer = manufacturerServiceDomain.getOne(manufacturerWrapper);
        storageInventory.setManufacturer(manufacturer);

        return storageInventory;
    }


    /**
     * 入库记录(流水)
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<StorageInventoryBatch> inFlowPage(StorageInventoryQuery pageQuery) {
        StorageInventory inventory = storageInventoryServiceDomain.lambdaQuery()
                .eq(StorageInventory::getId, pageQuery.getId()).one();
        return PageHelper.startPage(pageQuery, p ->
                storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, inventory.getCode())
                        .eq(StorageInventoryBatch::getWarehouseId, inventory.getWarehouseId())
                        .orderByDesc(StorageInventoryBatch::getCreatedAt).list()
        );
    }

    /**
     * 出库记录(流水)
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<StorageWarehouseFlow> outFlowPage(StorageInventoryQuery pageQuery) {
        StorageInventory inventory = storageInventoryServiceDomain.lambdaQuery().eq(StorageInventory::getId, pageQuery.getId()).one();
        LambdaQueryWrapper<StorageWarehouseFlow> outFlowWrapper = new LambdaQueryWrapper<>();
        outFlowWrapper.eq(StorageWarehouseFlow::getCode, inventory.getCode());
        outFlowWrapper.eq(StorageWarehouseFlow::getWarehouseId, inventory.getWarehouseId());
        outFlowWrapper.eq(StorageWarehouseFlow::getInOutType, pageQuery.getInOutType());
        outFlowWrapper.orderByDesc(StorageWarehouseFlow::getCreatedAt);

        return PageHelper.startPage(pageQuery, p -> storageWarehouseFlowServiceDomain.list(outFlowWrapper));
    }

    public DataGrid<InventoryUnsalableVo> getUnsalablePage(InventoryUnsalableQuery query) {
        return PageHelper.startPage(query, p -> storageInventoryServiceDomain.getInventoryUnsalableList(query)
        ).peek(p -> {
            p.setProductTreeDtoList(partProductTreeDomainService.getByPartId(p.getPartId()));
        });
    }

    public InventoryUnsalableVo getTotalInverntory(InventoryUnsalableQuery query) {
        InventoryUnsalableVo inventoryUnsalableVo = new InventoryUnsalableVo();
        List<InventoryUnsalableVo> inventoryUnsalableList = storageInventoryServiceDomain.getInventoryUnsalableList(query);
        if (CollectionUtils.isEmpty(inventoryUnsalableList)) {
            return inventoryUnsalableVo;
        }
        Long totalInventoryAmount = inventoryUnsalableList.stream().mapToLong(InventoryUnsalableVo::getInventoryAmount).sum();
        Integer totalInventoryNum = inventoryUnsalableList.stream().mapToInt(InventoryUnsalableVo::getInventoryNum).sum();
        inventoryUnsalableVo.setInventoryAmount(totalInventoryAmount);
        inventoryUnsalableVo.setInventoryNum(totalInventoryNum);
        return inventoryUnsalableVo;
    }

    public boolean inventoryBatchCalibrate() {
        List<StorageInventoryBatch> storageInventoryBatches = storageInventoryBatchServiceDomain.lambdaQuery()
                .list();
        for (StorageInventoryBatch storageInventoryBatch : storageInventoryBatches) {
            List<StorageWarehouseFlow> storageWarehouseFlows = storageWarehouseFlowServiceDomain.lambdaQuery()
                    .eq(StorageWarehouseFlow::getBatchCode, storageInventoryBatch.getBatchCode())
                    .eq(StorageWarehouseFlow::getCode, storageInventoryBatch.getCode())
                    .list();
            if (CollectionUtils.isEmpty(storageWarehouseFlows)) {
                continue;
            }
            Integer inWarehouseNumber = storageWarehouseFlows.stream()
                    .filter(f -> "1".equals(f.getInOutType()))
                    .mapToInt(StorageWarehouseFlow::getNumber).sum();

            Integer outWarehouseNumber = storageWarehouseFlows.stream()
                    .filter(f -> "2".equals(f.getInOutType()))
                    .mapToInt(StorageWarehouseFlow::getNumber).sum();

            Integer sumWarehouseNumber = inWarehouseNumber - outWarehouseNumber;
            storageInventoryBatch.setRemWarehouseNumber(sumWarehouseNumber);
            storageInventoryBatchServiceDomain.updateById(storageInventoryBatch);
        }
        return Boolean.TRUE;
    }


    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<StorageInventoryExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "库品导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    StorageInventoryExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 导入基站信息
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<StorageInventoryExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new InventoryExcelVerifyHandler());
            ExcelImportResult<StorageInventoryExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, StorageInventoryExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<StorageInventoryExcel>> codeListMap = excels.stream().collect(Collectors.groupingBy(StorageInventoryExcel::getCode));

            for (Map.Entry<String, List<StorageInventoryExcel>> entry : codeListMap.entrySet()) {
                List<StorageInventoryExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("库品编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }
        //存数据库
        if (com.hightop.fario.base.util.CollectionUtils.isNotEmpty(excels)) {
            List<StorageInventory> machines = new ArrayList<>();
            for (StorageInventoryExcel data : excels) {
                StorageInventory storageInventory = data.getOriginData() == null ? new StorageInventory() : data.getOriginData();
                BeanUtils.copyProperties(data, storageInventory);
                storageInventory.setOutWarehouseNumber(0);
                storageInventory.setRunWarehouseNumber(0);
                machines.add(storageInventory);
            }
            this.storageInventoryServiceDomain.saveOrUpdateBatch(machines);
        }
        return Boolean.TRUE;
    }

    public Boolean inventoryAlarm(String articleCode, Long warehouseId) {

        List<StorageInventory> storageInventorys = storageInventoryServiceDomain.lambdaQuery()
                .eq(StringUtils.isNotBlank(articleCode), StorageInventory::getCode, articleCode)
                .apply(StringUtils.isBlank(articleCode), " (sum_warehouse_number <= alarm_number or sum_warehouse_number <= safe_number)")
                .eq(StorageInventory::getWarehouseId, warehouseId)
                .list();
        if (CollectionUtils.isEmpty(storageInventorys)) {
            return Boolean.TRUE;
        }
        for (StorageInventory storageInventory : storageInventorys) {
            if (storageInventory.getAlarmNumber() == null || storageInventory.getAlarmNumber() <= 0) {
                continue;
            }

            // 获取各项数量，空值按0处理
            int sumWarehouseNumber = storageInventory.getSumWarehouseNumber() != null ? storageInventory.getSumWarehouseNumber() : 0;
            int runWarehouseNumber = storageInventory.getRunWarehouseNumber() != null ? storageInventory.getRunWarehouseNumber() : 0;
            int engineerNum = storageInventory.getEngineerNum() != null ? storageInventory.getEngineerNum() : 0;
            int alarmNumber = storageInventory.getAlarmNumber();

            // 业务需求：库存量 + 工程师数量 + 在途量 < 预警值 时，新增采购需求
            if (sumWarehouseNumber + engineerNum + runWarehouseNumber < alarmNumber) {
                // 检查是否已存在采购需求
                PurchaseRequireQuery query = new PurchaseRequireQuery();
                query.setArticleCode(storageInventory.getCode());
                query.setWarehouseId(warehouseId);
                DataGrid<PurchaseRequire> existingRequires = purchaseRequireService.page(query);

                // 计算需要的采购数量
                // 业务需求：采购数量 = 安全库存 - 库存量 - 工程师数量 - 在途量
                Integer purchaseNum;
                if (storageInventory.getSafeNumber() != null && storageInventory.getSafeNumber() > 0) {
                    purchaseNum = storageInventory.getSafeNumber() - sumWarehouseNumber - engineerNum - runWarehouseNumber;
                } else {
                    // 如果没有安全库存，采购到预警值的2倍
                    purchaseNum = (int)(storageInventory.getAlarmNumber() * 2) - sumWarehouseNumber - engineerNum - runWarehouseNumber;
                }

                if (purchaseNum <= 0) {
                    continue; // 不需要采购
                }

                if (existingRequires != null && existingRequires.getTotal() > 0) {
                    // 检查现有采购需求的状态和数量
                    boolean hasCreateStatus = false;
                    boolean hasActivePurchaseStatus = false; // 只考虑进行中的采购单
                    Long totalExistingNum = 0L;

                    for (PurchaseRequire existing : existingRequires.getRows()) {
                        if (PurchaseRequireStatusEnum.CREATE.equals(existing.getStatus())) {
                            hasCreateStatus = true;
                            totalExistingNum += existing.getNumber();
                        } else if (PurchaseRequireStatusEnum.PURCHASE.equals(existing.getStatus())) {
                            // 检查对应的采购单是否还在进行中（未完成）
                            // 如果采购单已完成（SUCCESS/COMPLETE/CLOSE），则不阻止创建新需求
                            // 这里简化处理：只要有PURCHASE状态就认为有进行中的采购
                            // 实际应该查询采购单状态，但为了性能考虑暂时这样处理
                            hasActivePurchaseStatus = true;
                            totalExistingNum += existing.getNumber();
                        }
                        // 已取消状态不参与计算，视为无效需求
                    }

                    if (hasActivePurchaseStatus) {
                        // 有进行中的采购单，暂不创建新需求
                        // 注意：如果采购单已完成但状态未更新，可能需要手动处理
                        continue;
                    }

                    if (hasCreateStatus) {
                        // 有上报状态的需求，检查数量是否足够
                        if (totalExistingNum >= purchaseNum) {
                            continue; // 现有数量足够，跳过
                        } else {
                            // 现有数量不够，更新为新的数量（只更新上报状态的）
                            for (PurchaseRequire existing : existingRequires.getRows()) {
                                if (PurchaseRequireStatusEnum.CREATE.equals(existing.getStatus())) {
                                    existing.setNumber(purchaseNum.longValue());
                                    purchaseRequireService.update(new PurchaseRequireModifyDto() {{
                                        setId(existing.getId());
                                        setNumber(purchaseNum.longValue());
                                        setStatus(PurchaseRequireStatusEnum.CREATE);
                                    }});
                                    break; // 只更新第一个上报状态的记录
                                }
                            }
                            continue;
                        }
                    }
                }

                // 没有现有需求或现有需求不满足条件，创建新的采购需求
                PurchaseRequire purchaseRequire = new PurchaseRequire();
                purchaseRequire.setArticleCode(storageInventory.getCode());
                purchaseRequire.setNumber(purchaseNum.longValue());
                purchaseRequire.setWarehouseId(warehouseId);
                purchaseRequire.setRequireSource(PurchaseRequireSourceEnum.ALARM);
                purchaseRequireService.save(purchaseRequire);
            }
        }
        return Boolean.TRUE;
    }

}
