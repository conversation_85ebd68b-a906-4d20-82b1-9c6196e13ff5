<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.StorageInventoryMapper">
    <select id="getInventoryList" resultType="com.hightop.benyin.storage.infrastructure.entity.StorageInventory">
        SELECT * FROM (
            SELECT
            t1.id,
            t1.code,
            t1.name,
            t1.location,
            t1.warehouse_id,
            t1.sum_warehouse_number,
            t1.alarm_number,
            t1.out_warehouse_number,
            t1.run_warehouse_number,
            t2.part_brand,
            t2.manufacturer_channel,
            t1.created_at,
            t1.average_price,
            t3.name as warehouseName,
            t3.type as warehouseType,
            t1.updated_at,
            exists (select 1 from tb_sale_sku t3 inner join tb_item t4 on t3.deleted=false and t3.item_id = t4.id and t4.deleted=false where t3.inv_sku_id = t1.id) as sale_status,
            t2.number_oem,
        ifnull(t4.number,0) engineer<PERSON>um,
        ifnull( t5.number,0) applyNum,
        ifnull(t4.number,0)+ifnull( t5.number,0)+t1.sum_warehouse_number as totalNum,
        ifnull(t6.batchInventoryAmount, 0) as batchInventoryAmount,
        ifnull(t6.batchInventoryNum, t1.sum_warehouse_number) as batchInventoryNum
            FROM b_storage_inventory t1
             JOIN b_storage_article t2 on t1.code = t2.code and t2.deleted = false
             JOIN b_storage_warehouse t3 on t1.warehouse_id = t3.id and t3.deleted = false
        left join (select sum(after_lock_num) number,article_code
        from tb_item_store where user_type='ENGINEER' and after_lock_num>0 group by article_code) t4 on t4.article_code=t1.code
        left join (select sum(after_lock_num) number,article_code
        from tb_item_store t
        where sku_source='APPLY' and after_lock_num>0
        group by article_code) t5 on t5.article_code = t1.code
        left join (
            SELECT
                b1.code,
                b1.warehouse_id,
                SUM(IFNULL(b1.audit_in_warehouse_number - IFNULL(b2.out_number, 0), 0) * IFNULL(b1.price, 0)) as batchInventoryAmount,
                SUM(IFNULL(b1.audit_in_warehouse_number - IFNULL(b2.out_number, 0), 0)) as batchInventoryNum
            FROM (
                SELECT batch_code, SUM(audit_in_warehouse_number) audit_in_warehouse_number, price, code, warehouse_id
                FROM b_storage_in_warehouse_goods
                GROUP BY code, batch_code, warehouse_id
            ) b1
            LEFT JOIN (
                SELECT batch_code, SUM(number) out_number, code, warehouse_id
                FROM b_storage_warehouse_flow
                WHERE in_out_type = 2
                GROUP BY code, batch_code, warehouse_id
            ) b2 ON b1.batch_code = b2.batch_code AND b1.code = b2.code AND b1.warehouse_id = b2.warehouse_id
            GROUP BY b1.code, b1.warehouse_id
        ) t6 on t6.code = t1.code and t6.warehouse_id = t1.warehouse_id
            WHERE t1.deleted = false
            <if test="null!=qo.warehouseId and qo.warehouseId!=''">
                and t1.warehouse_id like concat ('%',#{qo.warehouseId},'%')
            </if>
            <if test="null!=qo.code and qo.code!=''">
                and t1.code like concat ('%',#{qo.code},'%')
            </if>
            <if test="null!=qo.name and qo.name!=''">
                and t1.name like concat ('%',#{qo.name},'%')
            </if>
            <if test="null!=qo.partBrand and qo.partBrand!=''">
                and t2.part_brand like concat ('%',#{qo.partBrand},'%')
            </if>
            <if test="null!=qo.numberOem and qo.numberOem!=''">
                and t2.number_oem like concat ('%',#{qo.numberOem},'%')
            </if>
            <if test="null!=qo.manufacturerChannel and qo.manufacturerChannel!=''">
                and t2.manufacturer_channel = #{qo.manufacturerChannel}
            </if>
            <if test="null!=qo.minSumWarehouseNumber">
                and t1.sum_warehouse_number &gt;= #{qo.minSumWarehouseNumber}
            </if>
            <if test="null!=qo.maxSumWarehouseNumber">
                and t1.sum_warehouse_number &lt;= #{qo.maxSumWarehouseNumber}
            </if>
            <if test="null!=qo.minEngineerNum">
                and t4.number &gt;= #{qo.minEngineerNum}
            </if>
            <if test="null!=qo.maxEngineerNum">
                and t4.number &lt;= #{qo.maxEngineerNum}
            </if>
            <if test="null!=qo.minApplyNum">
                and t5.number &gt;= #{qo.minApplyNum}
            </if>
            <if test="null!=qo.maxApplyNum">
                and t5.number &lt;= #{qo.maxApplyNum}
            </if>
            <if test="null!=qo.minTotalNum">
                and  ifnull(t4.number,0)+ifnull( t5.number,0)+t1.sum_warehouse_number  &gt;= #{qo.minTotalNum}
            </if>
            <if test="null!=qo.maxTotalNum">
              and  ifnull(t4.number,0)+ifnull( t5.number,0)+t1.sum_warehouse_number &lt;= #{qo.maxTotalNum}
            </if>
            <if test="null!=qo.location and qo.location!=''">
                and t1.location like concat('%',#{qo.location},'%')
            </if>
        <if test="null!=qo.isAlarm">
            <choose>
                <when test="qo.isAlarm==true">
                    and t1.sum_warehouse_number &lt;= t1.alarm_number and t1.safe_number>0
                </when>
                <otherwise>
                    and t1.sum_warehouse_number &gt; t1.alarm_number
                </otherwise>
            </choose>
        </if>

        ) t
        <where>
            <if test="null != qo.saleStatus">
                t.sale_status = #{qo.saleStatus}
            </if>
        </where>
        order by t.id desc
    </select>
    <select id="getPCInventoryList" resultType="com.hightop.benyin.storage.infrastructure.entity.StorageInventory">
        SELECT * FROM (
        SELECT
        t1.id,
        t1.code,
        t1.name,
        t1.location,
        t1.warehouse_id,
        t1.sum_warehouse_number,
        t1.alarm_number,
        t1.out_warehouse_number,
        t1.run_warehouse_number,
        t2.part_brand,
        t1.created_at,
        t1.updated_at,
        exists (select 1 from tb_sale_sku t3 inner join tb_item t4 on t3.deleted=false and t3.item_id = t4.id and t4.deleted=false where t3.inv_sku_id = t1.id) as sale_status,
        t2.number_oem
        FROM b_storage_inventory t1
        JOIN b_storage_article t2 on t1.code = t2.code and t2.deleted = false
        WHERE t1.deleted = false
        <if test="null!=qo.warehouseId and qo.warehouseId!=''">
            and t1.warehouse_id like concat ('%',#{qo.warehouseId},'%')
        </if>
        <if test="null!=qo.code and qo.code!=''">
            and t1.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null!=qo.name and qo.name!=''">
            and t1.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null!=qo.partBrand and qo.partBrand!=''">
            and t2.part_brand like concat ('%',#{qo.partBrand},'%')
        </if>
        <if test="null!=qo.numberOem and qo.numberOem!=''">
            and t2.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null!=qo.manufacturerChannel and qo.manufacturerChannel!=''">
            and t2.manufacturer_channel = #{qo.manufacturerChannel}
        </if>
        <if test="null!=qo.minSumWarehouseNumber">
            and t1.sum_warehouse_number &gt;= #{qo.minSumWarehouseNumber}
        </if>
        <if test="null!=qo.maxSumWarehouseNumber">
            and t1.sum_warehouse_number &lt;= #{qo.maxSumWarehouseNumber}
        </if>
        <if test="null!=qo.minAlarmNumber">
            and t1.alarm_number &gt;= #{qo.minAlarmNumber}
        </if>
        <if test="null!=qo.maxAlarmNumber">
            and t1.alarm_number &lt;= #{qo.maxAlarmNumber}
        </if>
        <if test="null!=qo.isAlarm">
          <choose>
              <when test="qo.isAlarm==true">
                  and t1.sum_warehouse_number &lt;= t1.alarm_number
              </when>
              <otherwise>
                  and t1.sum_warehouse_number &gt; t1.alarm_number
              </otherwise>
          </choose>
        </if>

        <if test="null!=qo.location and qo.location!=''">
            and t1.location like concat('%',#{qo.location},'%')
        </if>
        ) t
        <where>
            <if test="null != qo.saleStatus">
                t.sale_status = #{qo.saleStatus}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="getTakeMachineDetail" resultType="com.hightop.benyin.storage.infrastructure.entity.TakeDetail">
        SELECT
        t.machine_num articleCode,t.pics_url imageFiles,t.purchase_price inventoryAmount,
            <choose>
            <when test="null != qo.takeStockId">
                 t2.id,t2.stock_num stockNum,t2.location,t2.article_name,t2.product_id,t2.full_id_path,t2.device_on,t2.origin_code,t2.host_type,t2.device_status,t2.machine_status,t2.device_status, 1 inventoryNum
            </when>
                <otherwise>
                    t.product_name articleName,t.location,t.product_id productId,t1.full_id_path,1 inventoryNum
                    ,t.device_on,t.origin_code,t.host_type,t.device_status,t.device_on,t.status machineStatus,t.device_status
                </otherwise>
            </choose>
        from b_machine t
            left join b_product_tree  t1 on t1.id = t.product_id
        <if test="null != qo.takeStockId">
            left join b_take_detail t2 on t2.article_code = t.machine_num and t2.take_stock_id =#{qo.takeStockId}
        </if>
        where  t.status in('INVENTORY','REPAIR')
          <if test="null!=qo.articleCode and qo.articleCode!=''">
            and t.machine_num like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null!=qo.articleName and qo.articleName!=''">
            <choose>
                <when test="null != qo.takeStockId">
                    and ( t.product_name like concat ('%',#{qo.articleName},'%')or t2.article_name like concat ('%',#{qo.articleName},'%'))
                </when>
                <otherwise>
                    and t.product_name like concat ('%',#{qo.articleName},'%')
                </otherwise>
            </choose>
        </if>

        <if test="null!=qo.location and qo.location!=''">
            <choose>
            <when test="null != qo.takeStockId">
                and ( t.location like concat ('%',#{qo.location},'%')or t2.location like concat ('%',#{qo.location},'%'))
            </when>
                <otherwise>
                    and t.location like concat ('%',#{qo.location},'%')
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.originCode and qo.originCode!=''">
            <choose>
                <when test="null != qo.takeStockId">
                    and ( t.origin_code like concat ('%',#{qo.originCode},'%')or t2.origin_code like concat ('%',#{qo.originCode},'%'))
                </when>
                <otherwise>
                    and t.origin_code like concat ('%',#{qo.originCode},'%')
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            <choose>
                <when test="null != qo.takeStockId">
                    AND (t.product_id in
                    <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                        or t2.product_id in
                    <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                )
                </when>
                <otherwise>
                    AND t.product_id in
                    <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.hostTypes and !qo.hostTypes.isEmpty()">
            <choose>
                <when test="null != qo.takeStockId">
                    AND (t.host_type in
                    <foreach collection="qo.hostTypes" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    or t2.host_type in
                    <foreach collection="qo.hostTypes" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND t.host_type in
                    <foreach collection="qo.hostTypes" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.deviceOn and !qo.deviceOn.isEmpty()">
            <choose>
                <when test="null != qo.takeStockId">
                    AND (t.device_on in
                    <foreach collection="qo.deviceOn" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    or t2.device_on in
                    <foreach collection="qo.deviceOn" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND t.device_on in
                    <foreach collection="qo.deviceOn" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.deviceStatus and !qo.deviceStatus.isEmpty()">
            <choose>
                <when test="null != qo.takeStockId">
                    AND (t.device_status in
                    <foreach collection="qo.deviceStatus" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    or t2.device_status in
                    <foreach collection="qo.deviceStatus" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND t.device_status in
                    <foreach collection="qo.deviceStatus" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.isTake and qo.takeStockId != null">
            <choose>
                <when test="qo.isTake == true ">
                    and t2.id is not null
                </when>
                <otherwise>
                    and t2.id is null
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.isNormal">
            <choose>
                <when test="qo.isNormal == true ">
                    and t2.stock_num>0
                </when>
                <otherwise>
                    and t2.stock_num=0
                </otherwise>
            </choose>
        </if>
<!--        <choose>-->
<!--            <when test="null != qo.takeStockId ">-->
<!--                order by t2.machine_num desc-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                order by t.location asc-->
<!--            </otherwise>-->
<!--        </choose>-->
        order by machine_num asc
    </select>

    <select id="getTakeDetailByStorageInventory" resultType="com.hightop.benyin.storage.infrastructure.entity.TakeDetail">
        SELECT
        t.code articleCode,t.name articleName,t1.number_oem numberOem,t.average_price price,t.sum_warehouse_number*ifnull(t.average_price,0) inventoryAmount,
        t.sum_warehouse_number inventoryNum,t1.image_files imageFiles,t1.product_id productId,t1.manufacturer_goods_name,t1.manufacturer_goods_code,
        t4.batch_code batcheCode,
        t4.price inPrice,
        IFNULL(t4.number-IFNULL(t3.number,0),0) AS num
        <choose>
            <when test="null != qo.takeStockId">
                , t2.id,t2.stock_num stockNum,t2.location,t2.manufacturer_channel
            </when>
            <otherwise>
                , t.location,t1.manufacturer_channel
            </otherwise>
        </choose>
        from b_storage_inventory t left join b_storage_article t1 on t.code = t1.code
        <include refid="fromAndWhere"></include>
        <choose>
            <when test="null != qo.takeStockId ">
                order by t2.id desc
            </when>
            <otherwise>
                order by t.location asc
            </otherwise>
        </choose>
    </select>
    <sql id="fromAndWhere">
        LEFT JOIN (SELECT
        t.batch_code,
        SUM(t.audit_in_warehouse_number) number,
        t.price,
        t.code
        FROM b_storage_in_warehouse_goods t
        WHERE
        t.warehouse_id = #{qo.warehouseId}
        GROUP BY t.code,t.batch_code) t4 ON t4.code = t.code
        LEFT JOIN
        (SELECT
        batch_code,
        SUM(number) number,
        `code`
        FROM b_storage_warehouse_flow
        WHERE in_out_type=2
        AND warehouse_id = #{qo.warehouseId}
        GROUP BY code,batch_code) t3
        ON t4.batch_code = t3.batch_code AND t4.code = t3.code
        <if test="null != qo.takeStockId">
            left join b_take_detail t2 on t2.article_code = t.code and t2.take_stock_id =#{qo.takeStockId}
        </if>
        where t.warehouse_id = #{qo.warehouseId} and t1.deleted = 0
        AND IFNULL(t4.number-IFNULL(t3.number,0),0) > 0
        <if test="null != qo.minNum ">
            and IFNULL(t4.number-IFNULL(t3.number,0),0) >= #{qo.minNum}
        </if>
        <if test="null != qo.maxNum ">
           <![CDATA[ and IFNULL(t4.number-IFNULL(t3.number,0),0) <= #{qo.maxNum} ]]>
        </if>
        <choose>
            <when test="null != qo.stockType and qo.stockType==1 ">
                and t1.product_id is not null
            </when>
            <otherwise>
                and t1.product_id is null
            </otherwise>
        </choose>
        <if test="null!=qo.numberOem and qo.numberOem!=''">
            and t1.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null!=qo.articleCode and qo.articleCode!=''">
            and t.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null!=qo.articleName and qo.articleName!=''">
            and t.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null!=qo.location and qo.location!=''">
            <choose>
                <when test="null != qo.takeStockId">
                    and ( t.location like concat ('%',#{qo.location},'%')or t2.location like concat ('%',#{qo.location},'%'))
                </when>
                <otherwise>
                    and t.location like concat ('%',#{qo.location},'%')
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.manufacturerChannel and qo.manufacturerChannel!=''">
            <choose>
                <when test="null != qo.takeStockId">
                    and ( t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')or t2.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%'))
                </when>
                <otherwise>
                    and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
                </otherwise>
            </choose>
        </if>
        <if test="null!=qo.manufacturerGoodsCode and qo.manufacturerGoodsCode!=''">
            and t1.manufacturer_goods_code like concat ('%',#{qo.manufacturerGoodsCode},'%')
        </if>
        <if test="null!=qo.manufacturerGoodsName and qo.manufacturerGoodsName!=''">
            and t1.manufacturer_goods_name like concat ('%',#{qo.manufacturerGoodsName},'%')
        </if>
        <if test="null!=qo.minInventoryNum">
            and t.sum_warehouse_number &gt;= #{qo.minInventoryNum}
        </if>
        <if test="null!=qo.maxInventoryNum">
            and t.sum_warehouse_number &lt;= #{qo.maxInventoryNum}
        </if>
        <if test="null!=qo.isTake and qo.takeStockId != null">
            <choose>
                <when test="qo.isTake == true ">
                    and t2.id is not null
                </when>
                <otherwise>
                    and t2.id is null
                </otherwise>
            </choose>
        </if>

        <if test="null!=qo.isNormal">
            <choose>
                <when test="qo.isNormal == true ">
                    and t2.stock_num=t2.inventory_num
                </when>
                <otherwise>
                    and t2.stock_num!=t2.inventory_num
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="stockDetailStatistics"
            resultType="com.hightop.benyin.storage.application.vo.TakeDetailStatistics">
        SELECT
        SUM( IFNULL(t4.number-IFNULL(t3.number,0),0)) inventoryNum,
        SUM( IFNULL(t4.number-IFNULL(t3.number,0),0) * ifnull( t4.price, 0 )) inventoryAmount
        from b_storage_inventory t left join b_storage_article t1 on t.code = t1.code
        <include refid="fromAndWhere"></include>
    </select>
    <select id="getInventoryUnsalableList" resultType="com.hightop.benyin.storage.application.vo.InventoryUnsalableVo">
        select t.id
        , t.code
        , t.name
        , t.location
        ,t.warehouseName
        ,t.part_id,
        t.part_brand,
        t.manufacturer_channel,
        t.image_files
        , t.numberOem
        , t.inventoryNum
        , t.salesNum
        , t.purchaseNum
        , t.avgSalesNum
        , t.finalSalesTime
        ,t.unsaleableNum
        , t.is_unsalable
        , t.price
        , t.inventoryAmount
        from (select t.id,
        t.code,
        t.name,
        t.location,
        tr.part_id,
        tw.name warehouseName ,
        tr.number_oem numberOem,
        t.sum_warehouse_number inventoryNum,
        t1.salesNum,
        t2.purchaseNum,
        ROUND(t1.salesNum / avg.num,0) avgSalesNum,
        t1.finalSalesTime,
        DATEDIFF(CURDATE(),ifnull(t1.finalSalesTime,t.created_at)) unsaleableNum,
        t.is_unsalable,
        tr.part_brand,
        tr.manufacturer_channel,
        tr.image_files,
        (select avg(price)
        from b_storage_in_warehouse_goods
        where code = t.code and warehouse_id = t.warehouse_id  and price is not null and price!=0 and in_warehouse_number>0) price,
        (select sum(b.rem_warehouse_number*b.price)
        from b_storage_inventory_batch b where b.code=t.code and b.warehouse_id=t.warehouse_id  and b.rem_warehouse_number>0) inventoryAmount
        from b_storage_inventory t
        left join b_storage_article tr on tr.code = t.code
        left join b_storage_warehouse tw on tw.id = t.warehouse_id
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            left join tb_part_product_tree c on tr.part_id = c.part_id
        </if>
        left join
            (select code,warehouse_id, sum(number) salesNum, max(created_at) finalSalesTime
            from b_storage_warehouse_flow
            where in_out_type = 2
            group by code,warehouse_id) t1 on t1.code = t.code and t1.warehouse_id = t.warehouse_id
        left join
            (select code,warehouse_id, sum(number) purchaseNum
            from b_storage_warehouse_flow
            where in_out_type = 1
            group by code,warehouse_id) t2 on t2.code = t.code and t2.warehouse_id = t.warehouse_id
        left join
            (select avg.code,warehouse_id, count(1) num
            from (select code,warehouse_id, DATE_FORMAT(created_at, '%Y-%m')
            from b_storage_warehouse_flow
            where in_out_type = 2
            group by code,warehouse_id, DATE_FORMAT(created_at, '%Y-%m')) avg
            group by code,warehouse_id) avg on avg.code = t.code  and avg.warehouse_id = t.warehouse_id
        where t.sum_warehouse_number > 0 and t.deleted = false and tw.status = '1401'
        <if test="null != qo.warehouseId">
            and t.warehouse_id =#{qo.warehouseId}
        </if>
        <if test="null != qo.warehouseName and qo.warehouseName!=''">
            and tw.name like concat ('%',#{qo.warehouseName},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(c.product_tree_ids,#{id})
            </foreach>
        </if>

        <if test="null != qo.numberOem and qo.numberOem!=''">
            and tr.number_oem  like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null != qo.partBrand and qo.partBrand!=''">
            and tr.part_brand  like concat ('%',#{qo.partBrand},'%')
        </if>

        <if test="null != qo.manufacturerChannel and qo.manufacturerChannel!=''">
            and tr.manufacturer_channel  like concat ('%',#{qo.manufacturerChannel},'%')
        </if>

        <if test="null != qo.code and qo.code!=''">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.name and qo.name!=''">
            and t.name like concat ('%',#{qo.name},'%')
        </if>

        <if test="null != qo.isUnsalable">
            and t.is_unsalable =#{qo.isUnsalable}
        </if>
        ) t
        where 1=1

        <if test="null != qo.minInventoryNum ">
            and t.inventoryNum &gt;= #{qo.minInventoryNum}
        </if>

        <if test="null != qo.maxInventoryNum ">
            and t.inventoryNum &lt;= #{qo.maxInventoryNum}
        </if>

        <if test="null != qo.minInventoryAmount ">
            and t.price * t.inventoryNum &gt;= #{qo.minInventoryAmount}
        </if>

        <if test="null != qo.maxInventoryAmount ">
            and t.price * t.inventoryNum &lt;= #{qo.maxInventoryAmount}
        </if>
        <if test="null != qo.minPrice ">
            and t.price &gt;= #{qo.minPrice}
        </if>

        <if test="null != qo.minPrice ">
            and t.price  &lt;= #{qo.maxPrice}
        </if>
        <if test="null != qo.minAvgSalesNum ">
            and t.avgSalesNum &gt;= #{qo.minAvgSalesNum}
        </if>

        <if test="null != qo.maxAvgSalesNum ">
            and t.avgSalesNum  &lt;= #{qo.maxAvgSalesNum}
        </if>

        <if test="null != qo.minUnsaleableNum ">
            and t.unsaleableNum &gt;= #{qo.minUnsaleableNum}
        </if>

        <if test="null != qo.maxUnsaleableNum ">
            and t.unsaleableNum  &lt;= #{qo.maxUnsaleableNum}
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.finalSalesTime &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.finalSalesTime &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        order by t.unsaleableNum desc
    </select>

    <select id="getFinanceInstoreList" resultType="com.hightop.benyin.statistics.application.vo.FinanceInstoreVO">
        select *
        from (select t.*,
                     round(t.amount / t.taxRate, 2)              noTaxAmount,
                     t.amount - round((t.amount / t.taxRate), 2) taxAmount
              from (select date_format(t.created_at, '%Y-%m-%d')                        createDate,
                           t.flow_id                                                    code,
                           1 type,
                           t3.code                                                      manufacturerCode,
                           t3.name                                                      manufacturerName,
                           t.code                                                       articleCode,
                           t4.name                                                      articleName,
                           t4.manufacturer_channel,
                           t4.unit,
                           t.number num,
                           round(t5.price / 100, 2)                                     price,
                           round(t5.price * t.number / 100, 2)                          amount,
                           case when t3.tax is null then 1.13 else 1 + t3.tax / 100 end taxRate,
                           ifnull(t3.tax, 13)                                           tax,
                           t1.remarks,
                           t7.code                                                      payOrderCode,
                           round(t7.amount / 100, 2)                                              payOrderAmount
                    from b_storage_warehouse_flow t
                             left join b_storage_in_warehouse t1 on t1.in_warehouse_id = t.flow_id
                             left join b_manufacturer_order t2 on t2.code = t1.shop_waybill
                             left join b_manufacturer t3 on t3.id = t2.manufacturer_id
                             left join b_storage_article t4 on t4.code = t.code
                             left join b_storage_in_warehouse_goods t5
                                       on t5.code = t.code and t.batch_code = t5.batch_code and t5.in_warehouse_id = t.flow_id
                             left join (
                                 -- 优先选择有效状态的付款单，避免数据重复
                                 select pd.manufacter_order_id, pd.payment_id
                                 from b_purchase_payment_detail pd
                                 inner join b_purchase_payment pp on pp.id = pd.payment_id
                                 inner join (
                                     select pd2.manufacter_order_id,
                                            max(case when pp2.status = 'COMPLETED' then pp2.created_at else null end) as completed_max_time,
                                            max(case when pp2.status in ('WAIT_PAY', 'WAIT_AUDIT') then pp2.created_at else null end) as pending_max_time,
                                            max(case when pp2.status not in ('CLOSED', 'REJECT') then pp2.created_at else null end) as valid_max_time
                                     from b_purchase_payment_detail pd2
                                     inner join b_purchase_payment pp2 on pp2.id = pd2.payment_id
                                     where pp2.deleted = 0
                                     group by pd2.manufacter_order_id
                                 ) latest on latest.manufacter_order_id = pd.manufacter_order_id
                                         and pp.created_at = coalesce(latest.completed_max_time, latest.pending_max_time, latest.valid_max_time)
                                 where pp.deleted = 0 and pp.status not in ('CLOSED', 'REJECT')
                             ) t6 on t6.manufacter_order_id = t2.id
                             left join b_purchase_payment t7 on t7.id = t6.payment_id
                    where t.type = 'purchase') t
              union all
              select t.*,
                     round(t.amount / t.taxRate, 2)              noTaxAmount,
                     t.amount - round((t.amount / t.taxRate), 2) taxAmount
              from (select date_format(t.created_at, '%Y-%m-%d')                        createDate,
                           t5.in_warehouse_id                                           code,
                            0 type,
                           t3.code                                                      manufacturerCode,
                           t3.name                                                      manufacturerName,
                           t.code                                                       articleCode,
                           t4.name                                                      articleName,
                           t4.manufacturer_channel,
                           t4.unit,
                           t.number num,
                           round(t5.price / 100, 2)                                     price,
                           round(t5.price * t.number / 100, 2)                          amount,
                           case when t3.tax is null then 1.13 else 1 + t3.tax / 100 end taxRate,
                           ifnull(t3.tax, 13)                                           tax,
                           t1.remarks,
                           t7.code                                                      payOrderCode,
                  round(t7.amount / 100, 2)                                   payOrderAmount
                    from b_storage_warehouse_flow t
                             left join b_storage_out_warehouse t1 on t1.out_warehouse_id = t.flow_id
                             left join b_manufacturer_return t2 on t2.code = t1.shop_waybill
                             left join b_manufacturer t3 on t3.id = t2.manufacturer_id
                             left join b_storage_article t4 on t4.code = t.code
                             left join b_storage_inventory_batch t5 on t5.code = t.code and t.batch_code = t5.batch_code
                             left join b_manufacturer_return tt on tt.code = t2.manufacturer_order_code
                             left join (
                                 -- 退库也使用相同的优先级逻辑
                                 select pd.manufacter_order_id, pd.payment_id
                                 from b_purchase_payment_detail pd
                                 inner join b_purchase_payment pp on pp.id = pd.payment_id
                                 inner join (
                                     select pd2.manufacter_order_id,
                                            max(case when pp2.status = 'COMPLETED' then pp2.created_at else null end) as completed_max_time,
                                            max(case when pp2.status in ('WAIT_PAY', 'WAIT_AUDIT') then pp2.created_at else null end) as pending_max_time,
                                            max(case when pp2.status not in ('CLOSED', 'REJECT') then pp2.created_at else null end) as valid_max_time
                                     from b_purchase_payment_detail pd2
                                     inner join b_purchase_payment pp2 on pp2.id = pd2.payment_id
                                     where pp2.deleted = 0
                                     group by pd2.manufacter_order_id
                                 ) latest on latest.manufacter_order_id = pd.manufacter_order_id
                                         and pp.created_at = coalesce(latest.completed_max_time, latest.pending_max_time, latest.valid_max_time)
                                 where pp.deleted = 0 and pp.status not in ('CLOSED', 'REJECT')
                             ) t6 on t6.manufacter_order_id = tt.id
                             left join b_purchase_payment t7 on t7.id = t6.payment_id
                    where t.type = 'purchase_return') t
             ) t where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.articleCode like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.payOrderCode and '' != qo.payOrderCode ">
            and t.payOrderCode like concat ('%',#{qo.payOrderCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t.articleName like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.status and '' != qo.status ">
            and t.status = #{qo.status}
        </if>

        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t.manufacturerCode like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t.manufacturerName like concat ('%',#{qo.manufacturerName},'%')
        </if>
        order by t.code desc
    </select>
</mapper>